# propositions = ['P', 'Q', 'R']
# expression = "(P & Q) > R"
# " | ".join(['P', 'Q', 'R']) = "P | Q | R"
# header = "P | Q | R || (P & Q) > R"


--- Truth Table ---
P | Q | R || (P & Q) > R
--------------------------

Creates Cartesian product of input iterables
[True, False] repeated len(propositions) times
Generates all possible combinations
For 3 propositions ['P', 'Q', 'R']:
python# repeat=3, so [True, False] × [True, False] × [True, False]
# Results in 2³ = 8 combinations

for combo ក្នុង value_combinations៖ 
truth_values ​​= dict(zip(propositions, combo))
dict(zip()) ការពន្យល់៖
python# propositions = ['P', 'Q', 'R']
# បន្សំ = (ពិត មិនពិត)
# zip (សំណើ, បន្សំ) បង្កើតគូ៖ [('P', True), ('Q', False), ('R', True)]
# dict() បំប្លែងទៅជា៖ {'P': True, 'Q': False, 'R': True}

# ដំណើរការការវាយតម្លៃឡើងវិញសម្រាប់បន្សំនីមួយៗ 
rpn_queue = shunting_yard(tokens) # ញែកឡើងវិញដើម្បីឱ្យមានសុវត្ថិភាព ទោះបីជាមិនចាំបាច់យ៉ាងតឹងរ៉ឹងនៅទីនេះក៏ដោយ 
លទ្ធផល = evaluate_rpn(rpn_queue, truth_values)


# - Convert to string: True → "True", False → "False"  
# - Left-align in 5-character field: "True " or "False"
ចូលរួមជាមួយសញ្ញាបំបែក៖
python# បន្សំ = (ពិត, មិនពិត, ពិត)
# ["True", "False", "True"] ចូលរួមជាមួយ " |"
#លទ្ធផល៖ "ពិត | មិនពិត | ពិត"

ទម្រង់ជួរដេកពេញលេញ៖
python# f"{value_str} || {លទ្ធផល}"
#"ពិត|មិនពិត|ពិត||មិនពិត"

pythone លើកលែងតែ ValueError ជា e៖ 
print(f"កំហុសក្នុងការវាយតម្លៃសម្រាប់ {truth_values}: {e}")
សេណារីយ៉ូកំហុស៖

កន្សោម​ខុស​ទម្រង់
បាត់បង់តម្លៃការពិត
ប្រតិបត្តិករមិនត្រឹមត្រូវ
ជង់ underflow / លើស