
import heapq
from collections import defaultdict

# Graph representation based on the image
graph = {
    "Frankfurt": {"Mannheim": 85, "Wurzburg": 111},
    "Mannheim": {"Frankfurt": 85, "Karlsruhe": 67, "Stuttgart": 230},
    "Wurzburg": {"Frankfurt": 111, "Nurnberg": 104, "Stuttgart": 140, "Ulm": 183},
    "Karlsruhe": {"Mannheim": 67, "Stuttgart": 64, "Basel": 191},
    "Stuttgart": {"Mannheim": 230, "Wurzburg": 140, "Karlsruhe": 64, "Ulm": 107, "Memmingen": 184},
    "Ulm": {"Wurzburg": 183, "Stuttgart": 107, "Nurnberg": 171, "Memmingen": 55, "Munchen": 123},
    "Nurnberg": {"Wurzburg": 104, "Ulm": 171, "Bayreuth": 75, "Munchen": 170, "Passau": 220},
    "Bayreuth": {"Nurnberg": 75},
    "Munchen": {"Ulm": 123, "Nurnberg": 170, "Memmingen": 115, "Rosenheim": 59, "Landeck": 73},
    "Memmingen": {"Stuttgart": 184, "Ulm": 55, "Munchen": 115},
    "Rosenheim": {"Munchen": 59, "Passau": 189, "Salzburg": 81},
    "Passau": {"Nurnberg": 220, "Rosenheim": 189, "Linz": 102},
    "Salzburg": {"Rosenheim": 81, "Linz": 126},
    "Linz": {"Passau": 102, "Salzburg": 126},
    "Landeck": {"Munchen": 73, "Innsbruck": 93},
    "Innsbruck": {"Landeck": 93},
    "Basel": {"Karlsruhe": 191, "Zurich": 85, "Bern": 91},
    "Zurich": {"Basel": 85, "Bern": 120},
    "Bern": {"Basel": 91, "Zurich": 120}
}

def dijkstra(graph, start_vertex):
    """
    Dijkstra's algorithm to find shortest paths from start_vertex to all other vertices
    Returns: distances, previous_vertices, visited_order
    """
    # Initialize distances and previous vertices
    distances = {vertex: float('infinity') for vertex in graph}
    previous = {vertex: None for vertex in graph}
    distances[start_vertex] = 0

    # Priority queue to store (distance, vertex)
    pq = [(0, start_vertex)]
    visited = set()
    visited_order = []

    while pq:
        current_distance, current_vertex = heapq.heappop(pq)

        if current_vertex in visited:
            continue

        visited.add(current_vertex)
        visited_order.append(current_vertex)

        # Check all neighbors
        for neighbor, weight in graph[current_vertex].items():
            distance = current_distance + weight

            # If we found a shorter path, update it
            if distance < distances[neighbor]:
                distances[neighbor] = distance
                previous[neighbor] = current_vertex
                heapq.heappush(pq, (distance, neighbor))

    return distances, previous, visited_order

def print_table(distances, previous, visited_order, start_vertex):
    """Print a formatted table showing Vertex, Visited, Distance, Previous"""
    print(f"\nShortest paths from {start_vertex}:")
    print("-" * 60)
    print(f"{'Vertex':<15} {'Visited':<10} {'Distance':<12} {'Previous':<15}")
    print("-" * 60)

    # Sort vertices by visited order, then by name for unvisited
    all_vertices = list(distances.keys())
    visited_vertices = visited_order.copy()
    unvisited_vertices = [v for v in all_vertices if v not in visited_vertices]
    unvisited_vertices.sort()

    ordered_vertices = visited_vertices + unvisited_vertices

    for vertex in ordered_vertices:
        visited_status = "Yes" if vertex in visited_order else "No"
        distance = distances[vertex] if distances[vertex] != float('infinity') else "∞"
        prev_vertex = previous[vertex] if previous[vertex] else "-"

        print(f"{vertex:<15} {visited_status:<10} {str(distance):<12} {prev_vertex:<15}")

def get_path(previous, start, end):
    """Reconstruct the shortest path from start to end"""
    path = []
    current = end

    while current is not None:
        path.append(current)
        current = previous[current]

    path.reverse()

    if path[0] == start:
        return path
    else:
        return None  # No path exists

def main():
    start_city = "Memmingen"

    print("Graph Algorithm Study - Dijkstra's Shortest Path")
    print("=" * 60)

    # Run Dijkstra's algorithm
    distances, previous, visited_order = dijkstra(graph, start_city)

    # Print the table
    print_table(distances, previous, visited_order, start_city)

    print("\n" + "=" * 60)
    print("Interactive Path Finder")
    print("Enter a city name to find the shortest path and distance from Memmingen")
    print("Available cities:", ", ".join(sorted(graph.keys())))
    print("Type 'quit' to exit")

    while True:
        destination = input("\nEnter destination city: ").strip()

        if destination.lower() == 'quit':
            break

        # Find the city (case-insensitive)
        found_city = None
        for city in graph.keys():
            if city.lower() == destination.lower():
                found_city = city
                break

        if found_city:
            if found_city == start_city:
                print(f"You're already at {start_city}! Distance: 0")
            else:
                distance = distances[found_city]
                if distance == float('infinity'):
                    print(f"No path found from {start_city} to {found_city}")
                else:
                    path = get_path(previous, start_city, found_city)
                    print(f"\nShortest path from {start_city} to {found_city}:")
                    print(f"Distance: {distance} km")
                    print(f"Path: {' → '.join(path)}")
        else:
            print(f"City '{destination}' not found in the graph.")
            print("Available cities:", ", ".join(sorted(graph.keys())))

if __name__ == "__main__":
    main()