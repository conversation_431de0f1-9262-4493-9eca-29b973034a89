import re
import itertools

# --- 1. Definitions and Helper Functions ---

# Define all accepted operators and their corresponding standard symbol.
OPERATOR_MAP = {
    '~': '~', '!': '~', 'not': '~',
    '^': '&', '&': '&', '&&': '&', 'and': '&',
    'v': '|', '|': '|', '||': '|', 'or': '|',
    '->': '>', '=>': '>',
    '<->': '=', '<=>': '=',
}

# Define operator precedence and associativity.
# Higher number means higher precedence.
PRECEDENCE = {
    '~': {'prec': 4, 'assoc': 'Right'},  # Unary negation
    '&': {'prec': 3, 'assoc': 'Left'},   # Conjunction (AND)
    '|': {'prec': 2, 'assoc': 'Left'},   # Disjunction (OR)
    '>': {'prec': 1, 'assoc': 'Left'},   # Implication (IMPLIES)
    '=': {'prec': 0, 'assoc': 'Left'}    # Biconditional (IFF)
}

def perform_operation(operator, operands):
    """Performs the specified logical operation."""
    if operator == '~':
        return not operands[0]
    
    p, q = operands
    if operator == '&': return p and q
    if operator == '|': return p or q
    if operator == '>': return not p or q  # p -> q is equivalent to ~p v q
    if operator == '=': return p == q      # p <-> q is equivalent to p == q in Python

# --- 2. Shunting-Yard Algorithm for Parsing ---

def shunting_yard(tokens):
    """
    Converts a list of infix tokens to a postfix (RPN) queue
    using the Shunting-yard algorithm.
    """
    output_queue = []
    operator_stack = []

    for token in tokens:
        if token.isalpha():  # Proposition (variable)
            output_queue.append(token)
        elif token in PRECEDENCE:  # Operator
            while (operator_stack and operator_stack[-1] != '(' and
                   (PRECEDENCE[operator_stack[-1]]['prec'] > PRECEDENCE[token]['prec'] or
                    (PRECEDENCE[operator_stack[-1]]['prec'] == PRECEDENCE[token]['prec'] and
                     PRECEDENCE[token]['assoc'] == 'Left'))):
                output_queue.append(operator_stack.pop())
            operator_stack.append(token)
        elif token == '(':
            operator_stack.append(token)
        elif token == ')':
            while operator_stack and operator_stack[-1] != '(':
                output_queue.append(operator_stack.pop())
            if not operator_stack or operator_stack[-1] != '(':
                raise ValueError("Mismatched parentheses in expression.")
            operator_stack.pop()  # Pop the '('

    while operator_stack:
        op = operator_stack.pop()
        if op == '(':
            raise ValueError("Mismatched parentheses in expression.")
        output_queue.append(op)

    return output_queue

# --- 3. RPN Evaluator ---
# --- r \kang -3-4---
def evaluate_rpn(rpn_queue, truth_values):
    """Evaluates a postfix (RPN) expression queue."""
    eval_stack = []
    for token in rpn_queue:
        if token.isalpha():
            if token not in truth_values:
                raise ValueError(f"Truth value for proposition '{token}' not provided.")
            eval_stack.append(truth_values[token])
        elif token in PRECEDENCE:
            if token == '~':  # Unary operator
                if not eval_stack:
                    raise ValueError("Invalid expression: not enough operands for '~'.")
                operands = [eval_stack.pop()]
            else:  # Binary operator
                if len(eval_stack) < 2:
                    raise ValueError(f"Invalid expression: not enough operands for '{token}'.")
                # Note: order is important for non-commutative operators like implication
                q = eval_stack.pop()
                p = eval_stack.pop()
                operands = [p, q]
            
            result = perform_operation(token, operands)
            eval_stack.append(result)
    # if operator == '&': return p and q
    # if operator == '|': return p or q
    # if operator == '>': return not p or q  # p -> q is equivalent to ~p v q
    # if operator == '=': return p == q      # p <-> q is equivalent to p == q
    # Final result should be the only item left in the stack

   
    
    if len(eval_stack) != 1:
        raise ValueError("Invalid expression format.")
    return eval_stack[0]

# --- 4. Truth Table Generator ---

def generate_truth_table(expression, tokens, propositions):
    """Generates and prints a full truth table for the expression."""
    print("\n--- Truth Table ---")
    
    # Header
    header = " | ".join(propositions) + " || " + expression
    print(header)
    print("-" * len(header))

    # Generate all combinations of truth values (True/False)
    value_combinations = list(itertools.product([True, False], repeat=len(propositions)))

    for combo in value_combinations:
        truth_values = dict(zip(propositions, combo))
        
        try:
            # Re-run evaluation for each combination
            rpn_queue = shunting_yard(tokens) # Re-parse to be safe, though not strictly necessary here
            result = evaluate_rpn(rpn_queue, truth_values)

            # Print row
            value_str = " | ".join(f"{str(v):<5}" for v in combo)
            print(f"{value_str} || {result}")
        except ValueError as e:
            print(f"Error evaluating for {truth_values}: {e}")


# --- 5. Main Execution Block ---

def solve_logic_expression():
    """Main function to get user input and drive the evaluation."""
    print("Propositional Logic Evaluator")
    print("Accepted Operators:")
    print("  - NOT: ~, !, not")
    print("  - AND: ^, &, &&, and")
    print("  - OR:  v, |, ||, or")
    print("  - IMPLIES: ->, =>")
    print("  - IFF: <->, <=>")
    print("Use standard English letters for propositions (e.g., P, Q, R).")
    print("-" * 20)

    # --- Get User Input ---
    # Example 1: (P ^ Q) -> R
    # Example 2: ~(P v Q) ^ R
    expression_str = input("Enter the logical expression: ").strip()

    # Example 1: P=True, Q=True, R=False
    # Example 2: P=False, Q=False, R=True
    values_str = input("Enter truth values (e.g., P=True, Q=False, R=True): ").strip()
    
    try:
        # --- Pre-processing and Tokenization ---
        # 1. Standardize operators for easier parsing
        # Sort keys by length descending to replace longer operators first (e.g., '<->' before '->')
        sorted_ops = sorted(OPERATOR_MAP.keys(), key=len, reverse=True)
        processed_expr = expression_str
        for op in sorted_ops:
            # Use word boundaries for 'not', 'and', 'or' to avoid replacing parts of variable names
            if op in ['not', 'and', 'or']:
                processed_expr = re.sub(r'\b' + op + r'\b', OPERATOR_MAP[op], processed_expr, flags=re.IGNORECASE)
            else:
                processed_expr = processed_expr.replace(op, OPERATOR_MAP[op])

        # 2. Tokenize the standardized expression
        tokens = re.findall(r'([A-Za-z]+|\(|\)|~|&|\||>|=)', processed_expr)

        # 3. Parse user-provided truth values
        truth_values = {}
        for pair in values_str.replace(" ", "").split(','):
            if '=' in pair:
                var, val_str = pair.split('=')
                val = val_str.strip().lower() in ['true', 't', '1']
                truth_values[var.strip()] = val
        
        # --- Evaluation ---
        rpn_queue = shunting_yard(tokens)
        final_result = evaluate_rpn(rpn_queue, truth_values)
        
        print("\n--- Evaluation Result ---")
        print(f"Input Expression: {expression_str}")
        print(f"Truth Values: {truth_values}")
        print(f"Final Output: {final_result}")

        # --- Optional Truth Table ---
        show_table = input("\nShow full truth table for this expression? (y/n): ").lower()
        if show_table == 'y':
            # Find all unique propositions from the token list
            propositions = sorted(list(set(t for t in tokens if t.isalpha())))
            if not propositions:
                print("Cannot generate a truth table for an expression with no variables.")
            else:
                generate_truth_table(expression_str, tokens, propositions)

    except (ValueError, KeyError, IndexError) as e:
        print(f"\nAn error occurred: {e}")
    except Exception as e:
        print(f"\nAn unexpected error occurred: {e}")


if __name__ == "__main__":
    solve_logic_expression()